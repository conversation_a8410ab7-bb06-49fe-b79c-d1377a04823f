import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import AuthPage from '../components/Auth/AuthPage';
import DemoMode from '../components/DemoMode';
import VectalDashboard from '../components/VectalDashboard';

export default function Dashboard() {
  const { user, loading } = useAuth();

  console.log('Firebase API Key:', process.env.NEXT_PUBLIC_FIREBASE_API_KEY);
  console.log('All env vars:', {
    apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
    authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID
  });

  const isDemoMode = process.env.NEXT_PUBLIC_FIREBASE_API_KEY === 'demo-api-key' ||
                     !process.env.NEXT_PUBLIC_FIREBASE_API_KEY;

  console.log('Is demo mode:', isDemoMode);

  if (isDemoMode) {
    return <DemoMode />;
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background transition-colors">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return <AuthPage />;
  }

  return <VectalDashboard />;
}
