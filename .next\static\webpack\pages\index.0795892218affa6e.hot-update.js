"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./components/VectalTaskModal.js":
/*!***************************************!*\
  !*** ./components/VectalTaskModal.js ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ VectalTaskModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/store */ \"./lib/store.js\");\n/* harmony import */ var _ui_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/dialog */ \"./components/ui/dialog.js\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/button */ \"./components/ui/button.js\");\n/* harmony import */ var _ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/input */ \"./components/ui/input.js\");\n/* harmony import */ var _ui_textarea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/textarea */ \"./components/ui/textarea.js\");\n/* harmony import */ var _ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/select */ \"./components/ui/select.js\");\n/* harmony import */ var _ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ui/badge */ \"./components/ui/badge.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Flag_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Flag,Tag,X!=!lucide-react */ \"__barrel_optimize__?names=Calendar,Clock,Flag,Tag,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction VectalTaskModal(param) {\n    let { isOpen, onClose, task = null } = param;\n    _s();\n    const { addTask, updateTask } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_2__.useTaskStore)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: (task === null || task === void 0 ? void 0 : task.title) || \"\",\n        description: (task === null || task === void 0 ? void 0 : task.description) || \"\",\n        status: (task === null || task === void 0 ? void 0 : task.status) || \"todo\",\n        priority: (task === null || task === void 0 ? void 0 : task.priority) || \"medium\",\n        dueDate: (task === null || task === void 0 ? void 0 : task.dueDate) || \"\",\n        tags: (task === null || task === void 0 ? void 0 : task.tags) || []\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [newTag, setNewTag] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.title.trim()) {\n            newErrors.title = \"Task title is required\";\n        }\n        if (formData.title.length > 100) {\n            newErrors.title = \"Task title must be less than 100 characters\";\n        }\n        if (formData.description.length > 500) {\n            newErrors.description = \"Description must be less than 500 characters\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) return;\n        setLoading(true);\n        try {\n            console.log(\"Submitting task with data:\", formData);\n            if (task) {\n                await updateTask(task.id, formData);\n            } else {\n                await addTask(formData);\n            }\n            onClose();\n            setFormData({\n                title: \"\",\n                description: \"\",\n                status: \"todo\",\n                priority: \"medium\",\n                dueDate: \"\",\n                tags: []\n            });\n            setErrors({});\n        } catch (error) {\n            console.error(\"Failed to save task:\", error);\n            let errorMessage = \"Failed to save task. Please try again.\";\n            if (error.message === \"User not authenticated\") {\n                errorMessage = \"Please log in to create tasks.\";\n            } else if (error.message) {\n                errorMessage = error.message;\n            }\n            setErrors({\n                submit: errorMessage\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: \"\"\n                }));\n        }\n    };\n    const addTag = ()=>{\n        if (newTag.trim() && !formData.tags.includes(newTag.trim())) {\n            setFormData((prev)=>({\n                    ...prev,\n                    tags: [\n                        ...prev.tags,\n                        newTag.trim()\n                    ]\n                }));\n            setNewTag(\"\");\n        }\n    };\n    const removeTag = (tagToRemove)=>{\n        setFormData((prev)=>({\n                ...prev,\n                tags: prev.tags.filter((tag)=>tag !== tagToRemove)\n            }));\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && e.target.name === \"newTag\") {\n            e.preventDefault();\n            addTag();\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case \"high\":\n                return \"destructive\";\n            case \"medium\":\n                return \"warning\";\n            case \"low\":\n                return \"success\";\n            default:\n                return \"secondary\";\n        }\n    };\n    const getStatusLabel = (status)=>{\n        switch(status){\n            case \"todo\":\n                return \"To Do\";\n            case \"inprogress\":\n                return \"In Progress\";\n            case \"done\":\n                return \"Done\";\n            default:\n                return status;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n        open: isOpen,\n        onOpenChange: onClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n            className: \"sm:max-w-[500px] max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Flag_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__.Tag, {\n                                className: \"h-5 w-5 text-primary\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, this),\n                            task ? \"Edit Task\" : \"Create New Task\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"title\",\n                                    className: \"text-sm font-medium text-foreground\",\n                                    children: \"Task Title *\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                    id: \"title\",\n                                    value: formData.title,\n                                    onChange: (e)=>handleInputChange(\"title\", e.target.value),\n                                    placeholder: \"Enter task title...\",\n                                    className: errors.title ? \"border-destructive focus:border-destructive\" : \"\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this),\n                                errors.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-destructive\",\n                                    children: errors.title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"status\",\n                                            className: \"text-sm font-medium text-foreground\",\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                            value: formData.status,\n                                            onValueChange: (value)=>handleInputChange(\"status\", value),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {}, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                            value: \"todo\",\n                                                            children: \"To Do\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                                            lineNumber: 149,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                            value: \"inprogress\",\n                                                            children: \"In Progress\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                                            lineNumber: 150,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                            value: \"done\",\n                                                            children: \"Done\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"priority\",\n                                            className: \"text-sm font-medium text-foreground\",\n                                            children: \"Priority\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                            value: formData.priority,\n                                            onValueChange: (value)=>handleInputChange(\"priority\", value),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {}, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                            value: \"high\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Flag_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__.Flag, {\n                                                                        className: \"h-3 w-3 text-destructive\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                                                        lineNumber: 166,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"High Priority\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                                                lineNumber: 165,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                            value: \"medium\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Flag_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__.Flag, {\n                                                                        className: \"h-3 w-3 text-warning\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                                                        lineNumber: 172,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"Medium Priority\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                                                lineNumber: 171,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                            value: \"low\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Flag_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__.Flag, {\n                                                                        className: \"h-3 w-3 text-success\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                                                        lineNumber: 178,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"Low Priority\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                                                lineNumber: 177,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"dueDate\",\n                                    className: \"text-sm font-medium text-foreground\",\n                                    children: \"Due Date\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            id: \"dueDate\",\n                                            type: \"date\",\n                                            value: formData.dueDate,\n                                            onChange: (e)=>handleInputChange(\"dueDate\", e.target.value),\n                                            className: \"pl-10\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                            lineNumber: 191,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Flag_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__.Calendar, {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"description\",\n                                    className: \"text-sm font-medium text-foreground\",\n                                    children: \"Description\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                    id: \"description\",\n                                    value: formData.description,\n                                    onChange: (e)=>handleInputChange(\"description\", e.target.value),\n                                    placeholder: \"Add task description...\",\n                                    className: \"min-h-[100px] \".concat(errors.description ? \"border-destructive focus:border-destructive\" : \"\")\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this),\n                                errors.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-destructive\",\n                                    children: errors.description\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                    lineNumber: 213,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-sm font-medium text-foreground\",\n                                    children: \"Tags\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            name: \"newTag\",\n                                            value: newTag,\n                                            onChange: (e)=>setNewTag(e.target.value),\n                                            onKeyPress: handleKeyPress,\n                                            placeholder: \"Add tag...\",\n                                            className: \"flex-1\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"button\",\n                                            onClick: addTag,\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            children: \"Add\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this),\n                                formData.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 mt-2\",\n                                    children: formData.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                            variant: \"secondary\",\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                tag,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>removeTag(tag),\n                                                    className: \"ml-1 hover:text-destructive\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Flag_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__.X, {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                            lineNumber: 234,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                    lineNumber: 232,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this),\n                        errors.submit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 bg-destructive/10 border border-destructive/20 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-destructive\",\n                                children: errors.submit\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                lineNumber: 250,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                            lineNumber: 249,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogFooter, {\n                            className: \"gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    onClick: onClose,\n                                    disabled: loading,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    type: \"submit\",\n                                    disabled: loading,\n                                    children: loading ? \"Saving...\" : task ? \"Update Task\" : \"Create Task\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                                    lineNumber: 257,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n            lineNumber: 116,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalTaskModal.js\",\n        lineNumber: 115,\n        columnNumber: 5\n    }, this);\n}\n_s(VectalTaskModal, \"WaNU4W69i7lntoLVQqUVy4hVc9I=\", false, function() {\n    return [\n        _lib_store__WEBPACK_IMPORTED_MODULE_2__.useTaskStore\n    ];\n});\n_c = VectalTaskModal;\nvar _c;\n$RefreshReg$(_c, \"VectalTaskModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1ZlY3RhbFRhc2tNb2RhbC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBaUM7QUFDVztBQUNpRDtBQUN4RDtBQUNGO0FBQ007QUFDbUQ7QUFDekQ7QUFDMEI7QUFDOUMsU0FBU3FCLGdCQUFnQixLQUFnQztRQUFoQyxFQUFFQyxNQUFNLEVBQUVDLE9BQU8sRUFBRUMsT0FBTyxJQUFJLEVBQUUsR0FBaEM7O0lBQ3RDLE1BQU0sRUFBRUMsT0FBTyxFQUFFQyxVQUFVLEVBQUUsR0FBR3pCLHdEQUFZQTtJQUM1QyxNQUFNLENBQUMwQixTQUFTQyxXQUFXLEdBQUc1QiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUM2QixVQUFVQyxZQUFZLEdBQUc5QiwrQ0FBUUEsQ0FBQztRQUN2QytCLE9BQU9QLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTU8sS0FBSyxLQUFJO1FBQ3RCQyxhQUFhUixDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1RLFdBQVcsS0FBSTtRQUNsQ0MsUUFBUVQsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNUyxNQUFNLEtBQUk7UUFDeEJDLFVBQVVWLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTVUsUUFBUSxLQUFJO1FBQzVCQyxTQUFTWCxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1XLE9BQU8sS0FBSTtRQUMxQkMsTUFBTVosQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNWSxJQUFJLEtBQUksRUFBRTtJQUN4QjtJQUNBLE1BQU0sQ0FBQ0MsUUFBUUMsVUFBVSxHQUFHdEMsK0NBQVFBLENBQUMsQ0FBQztJQUN0QyxNQUFNLENBQUN1QyxRQUFRQyxVQUFVLEdBQUd4QywrQ0FBUUEsQ0FBQztJQUNyQyxNQUFNeUMsZUFBZTtRQUNuQixNQUFNQyxZQUFZLENBQUM7UUFDbkIsSUFBSSxDQUFDYixTQUFTRSxLQUFLLENBQUNZLElBQUksSUFBSTtZQUMxQkQsVUFBVVgsS0FBSyxHQUFHO1FBQ3BCO1FBQ0EsSUFBSUYsU0FBU0UsS0FBSyxDQUFDYSxNQUFNLEdBQUcsS0FBSztZQUMvQkYsVUFBVVgsS0FBSyxHQUFHO1FBQ3BCO1FBQ0EsSUFBSUYsU0FBU0csV0FBVyxDQUFDWSxNQUFNLEdBQUcsS0FBSztZQUNyQ0YsVUFBVVYsV0FBVyxHQUFHO1FBQzFCO1FBQ0FNLFVBQVVJO1FBQ1YsT0FBT0csT0FBT0MsSUFBSSxDQUFDSixXQUFXRSxNQUFNLEtBQUs7SUFDM0M7SUFDQSxNQUFNRyxlQUFlLE9BQU9DO1FBQzFCQSxFQUFFQyxjQUFjO1FBQ2hCLElBQUksQ0FBQ1IsZ0JBQWdCO1FBQ3JCYixXQUFXO1FBQ1gsSUFBSTtZQUNGc0IsUUFBUUMsR0FBRyxDQUFDLDhCQUE4QnRCO1lBQzFDLElBQUlMLE1BQU07Z0JBQ1IsTUFBTUUsV0FBV0YsS0FBSzRCLEVBQUUsRUFBRXZCO1lBQzVCLE9BQU87Z0JBQ0wsTUFBTUosUUFBUUk7WUFDaEI7WUFDQU47WUFDQU8sWUFBWTtnQkFDVkMsT0FBTztnQkFDUEMsYUFBYTtnQkFDYkMsUUFBUTtnQkFDUkMsVUFBVTtnQkFDVkMsU0FBUztnQkFDVEMsTUFBTSxFQUFFO1lBQ1Y7WUFDQUUsVUFBVSxDQUFDO1FBQ2IsRUFBRSxPQUFPZSxPQUFPO1lBQ2RILFFBQVFHLEtBQUssQ0FBQyx3QkFBd0JBO1lBQ3RDLElBQUlDLGVBQWU7WUFDbkIsSUFBSUQsTUFBTUUsT0FBTyxLQUFLLDBCQUEwQjtnQkFDOUNELGVBQWU7WUFDakIsT0FBTyxJQUFJRCxNQUFNRSxPQUFPLEVBQUU7Z0JBQ3hCRCxlQUFlRCxNQUFNRSxPQUFPO1lBQzlCO1lBQ0FqQixVQUFVO2dCQUFFa0IsUUFBUUY7WUFBYTtRQUNuQyxTQUFVO1lBQ1IxQixXQUFXO1FBQ2I7SUFDRjtJQUNBLE1BQU02QixvQkFBb0IsQ0FBQ0MsT0FBT0M7UUFDaEM3QixZQUFZOEIsQ0FBQUEsT0FBUztnQkFBRSxHQUFHQSxJQUFJO2dCQUFFLENBQUNGLE1BQU0sRUFBRUM7WUFBTTtRQUMvQyxJQUFJdEIsTUFBTSxDQUFDcUIsTUFBTSxFQUFFO1lBQ2pCcEIsVUFBVXNCLENBQUFBLE9BQVM7b0JBQUUsR0FBR0EsSUFBSTtvQkFBRSxDQUFDRixNQUFNLEVBQUU7Z0JBQUc7UUFDNUM7SUFDRjtJQUNBLE1BQU1HLFNBQVM7UUFDYixJQUFJdEIsT0FBT0ksSUFBSSxNQUFNLENBQUNkLFNBQVNPLElBQUksQ0FBQzBCLFFBQVEsQ0FBQ3ZCLE9BQU9JLElBQUksS0FBSztZQUMzRGIsWUFBWThCLENBQUFBLE9BQVM7b0JBQ25CLEdBQUdBLElBQUk7b0JBQ1B4QixNQUFNOzJCQUFJd0IsS0FBS3hCLElBQUk7d0JBQUVHLE9BQU9JLElBQUk7cUJBQUc7Z0JBQ3JDO1lBQ0FILFVBQVU7UUFDWjtJQUNGO0lBQ0EsTUFBTXVCLFlBQVksQ0FBQ0M7UUFDakJsQyxZQUFZOEIsQ0FBQUEsT0FBUztnQkFDbkIsR0FBR0EsSUFBSTtnQkFDUHhCLE1BQU13QixLQUFLeEIsSUFBSSxDQUFDNkIsTUFBTSxDQUFDQyxDQUFBQSxNQUFPQSxRQUFRRjtZQUN4QztJQUNGO0lBQ0EsTUFBTUcsaUJBQWlCLENBQUNuQjtRQUN0QixJQUFJQSxFQUFFb0IsR0FBRyxLQUFLLFdBQVdwQixFQUFFcUIsTUFBTSxDQUFDQyxJQUFJLEtBQUssVUFBVTtZQUNuRHRCLEVBQUVDLGNBQWM7WUFDaEJZO1FBQ0Y7SUFDRjtJQUNBLE1BQU1VLG1CQUFtQixDQUFDckM7UUFDeEIsT0FBUUE7WUFDTixLQUFLO2dCQUFRLE9BQU87WUFDcEIsS0FBSztnQkFBVSxPQUFPO1lBQ3RCLEtBQUs7Z0JBQU8sT0FBTztZQUNuQjtnQkFBUyxPQUFPO1FBQ2xCO0lBQ0Y7SUFDQSxNQUFNc0MsaUJBQWlCLENBQUN2QztRQUN0QixPQUFRQTtZQUNOLEtBQUs7Z0JBQVEsT0FBTztZQUNwQixLQUFLO2dCQUFjLE9BQU87WUFDMUIsS0FBSztnQkFBUSxPQUFPO1lBQ3BCO2dCQUFTLE9BQU9BO1FBQ2xCO0lBQ0Y7SUFDQSxxQkFDRSw4REFBQy9CLDhDQUFNQTtRQUFDdUUsTUFBTW5EO1FBQVFvRCxjQUFjbkQ7a0JBQ2xDLDRFQUFDcEIscURBQWFBO1lBQUN3RSxXQUFVOzs4QkFDdkIsOERBQUN2RSxvREFBWUE7OEJBQ1gsNEVBQUNDLG1EQUFXQTt3QkFBQ3NFLFdBQVU7OzBDQUNyQiw4REFBQ3hELDhGQUFHQTtnQ0FBQ3dELFdBQVU7Ozs7Ozs0QkFDZG5ELE9BQU8sY0FBYzs7Ozs7Ozs7Ozs7OzhCQUcxQiw4REFBQ29EO29CQUFLQyxVQUFVOUI7b0JBQWM0QixXQUFVOztzQ0FDdEMsOERBQUNHOzRCQUFJSCxXQUFVOzs4Q0FDYiw4REFBQ0k7b0NBQU1DLFNBQVE7b0NBQVFMLFdBQVU7OENBQXNDOzs7Ozs7OENBR3ZFLDhEQUFDbkUsNENBQUtBO29DQUNKNEMsSUFBRztvQ0FDSE8sT0FBTzlCLFNBQVNFLEtBQUs7b0NBQ3JCa0QsVUFBVSxDQUFDakMsSUFBTVMsa0JBQWtCLFNBQVNULEVBQUVxQixNQUFNLENBQUNWLEtBQUs7b0NBQzFEdUIsYUFBWTtvQ0FDWlAsV0FBV3RDLE9BQU9OLEtBQUssR0FBRyxnREFBZ0Q7Ozs7OztnQ0FFM0VNLE9BQU9OLEtBQUssa0JBQ1gsOERBQUNvRDtvQ0FBRVIsV0FBVTs4Q0FBNEJ0QyxPQUFPTixLQUFLOzs7Ozs7Ozs7Ozs7c0NBR3pELDhEQUFDK0M7NEJBQUlILFdBQVU7OzhDQUNiLDhEQUFDRztvQ0FBSUgsV0FBVTs7c0RBQ2IsOERBQUNJOzRDQUFNQyxTQUFROzRDQUFTTCxXQUFVO3NEQUFzQzs7Ozs7O3NEQUd4RSw4REFBQ2pFLDhDQUFNQTs0Q0FBQ2lELE9BQU85QixTQUFTSSxNQUFNOzRDQUFFbUQsZUFBZSxDQUFDekIsUUFBVUYsa0JBQWtCLFVBQVVFOzs4REFDcEYsOERBQUM5QyxxREFBYUE7OERBQ1osNEVBQUNDLG1EQUFXQTs7Ozs7Ozs7Ozs4REFFZCw4REFBQ0gscURBQWFBOztzRUFDWiw4REFBQ0Msa0RBQVVBOzREQUFDK0MsT0FBTTtzRUFBTzs7Ozs7O3NFQUN6Qiw4REFBQy9DLGtEQUFVQTs0REFBQytDLE9BQU07c0VBQWE7Ozs7OztzRUFDL0IsOERBQUMvQyxrREFBVUE7NERBQUMrQyxPQUFNO3NFQUFPOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBSS9CLDhEQUFDbUI7b0NBQUlILFdBQVU7O3NEQUNiLDhEQUFDSTs0Q0FBTUMsU0FBUTs0Q0FBV0wsV0FBVTtzREFBc0M7Ozs7OztzREFHMUUsOERBQUNqRSw4Q0FBTUE7NENBQUNpRCxPQUFPOUIsU0FBU0ssUUFBUTs0Q0FBRWtELGVBQWUsQ0FBQ3pCLFFBQVVGLGtCQUFrQixZQUFZRTs7OERBQ3hGLDhEQUFDOUMscURBQWFBOzhEQUNaLDRFQUFDQyxtREFBV0E7Ozs7Ozs7Ozs7OERBRWQsOERBQUNILHFEQUFhQTs7c0VBQ1osOERBQUNDLGtEQUFVQTs0REFBQytDLE9BQU07c0VBQ2hCLDRFQUFDbUI7Z0VBQUlILFdBQVU7O2tGQUNiLDhEQUFDekQsK0ZBQUlBO3dFQUFDeUQsV0FBVTs7Ozs7O29FQUE2Qjs7Ozs7Ozs7Ozs7O3NFQUlqRCw4REFBQy9ELGtEQUFVQTs0REFBQytDLE9BQU07c0VBQ2hCLDRFQUFDbUI7Z0VBQUlILFdBQVU7O2tGQUNiLDhEQUFDekQsK0ZBQUlBO3dFQUFDeUQsV0FBVTs7Ozs7O29FQUF5Qjs7Ozs7Ozs7Ozs7O3NFQUk3Qyw4REFBQy9ELGtEQUFVQTs0REFBQytDLE9BQU07c0VBQ2hCLDRFQUFDbUI7Z0VBQUlILFdBQVU7O2tGQUNiLDhEQUFDekQsK0ZBQUlBO3dFQUFDeUQsV0FBVTs7Ozs7O29FQUF5Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQVFyRCw4REFBQ0c7NEJBQUlILFdBQVU7OzhDQUNiLDhEQUFDSTtvQ0FBTUMsU0FBUTtvQ0FBVUwsV0FBVTs4Q0FBc0M7Ozs7Ozs4Q0FHekUsOERBQUNHO29DQUFJSCxXQUFVOztzREFDYiw4REFBQ25FLDRDQUFLQTs0Q0FDSjRDLElBQUc7NENBQ0hpQyxNQUFLOzRDQUNMMUIsT0FBTzlCLFNBQVNNLE9BQU87NENBQ3ZCOEMsVUFBVSxDQUFDakMsSUFBTVMsa0JBQWtCLFdBQVdULEVBQUVxQixNQUFNLENBQUNWLEtBQUs7NENBQzVEZ0IsV0FBVTs7Ozs7O3NEQUVaLDhEQUFDM0QsbUdBQVFBOzRDQUFDMkQsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUd4Qiw4REFBQ0c7NEJBQUlILFdBQVU7OzhDQUNiLDhEQUFDSTtvQ0FBTUMsU0FBUTtvQ0FBY0wsV0FBVTs4Q0FBc0M7Ozs7Ozs4Q0FHN0UsOERBQUNsRSxrREFBUUE7b0NBQ1AyQyxJQUFHO29DQUNITyxPQUFPOUIsU0FBU0csV0FBVztvQ0FDM0JpRCxVQUFVLENBQUNqQyxJQUFNUyxrQkFBa0IsZUFBZVQsRUFBRXFCLE1BQU0sQ0FBQ1YsS0FBSztvQ0FDaEV1QixhQUFZO29DQUNaUCxXQUFXLGlCQUF5RixPQUF4RXRDLE9BQU9MLFdBQVcsR0FBRyxnREFBZ0Q7Ozs7OztnQ0FFbEdLLE9BQU9MLFdBQVcsa0JBQ2pCLDhEQUFDbUQ7b0NBQUVSLFdBQVU7OENBQTRCdEMsT0FBT0wsV0FBVzs7Ozs7Ozs7Ozs7O3NDQUcvRCw4REFBQzhDOzRCQUFJSCxXQUFVOzs4Q0FDYiw4REFBQ0k7b0NBQU1KLFdBQVU7OENBQXNDOzs7Ozs7OENBQ3ZELDhEQUFDRztvQ0FBSUgsV0FBVTs7c0RBQ2IsOERBQUNuRSw0Q0FBS0E7NENBQ0o4RCxNQUFLOzRDQUNMWCxPQUFPcEI7NENBQ1AwQyxVQUFVLENBQUNqQyxJQUFNUixVQUFVUSxFQUFFcUIsTUFBTSxDQUFDVixLQUFLOzRDQUN6QzJCLFlBQVluQjs0Q0FDWmUsYUFBWTs0Q0FDWlAsV0FBVTs7Ozs7O3NEQUVaLDhEQUFDcEUsOENBQU1BOzRDQUFDOEUsTUFBSzs0Q0FBU0UsU0FBUzFCOzRDQUFRMkIsU0FBUTs0Q0FBVUMsTUFBSztzREFBSzs7Ozs7Ozs7Ozs7O2dDQUlwRTVELFNBQVNPLElBQUksQ0FBQ1EsTUFBTSxHQUFHLG1CQUN0Qiw4REFBQ2tDO29DQUFJSCxXQUFVOzhDQUNaOUMsU0FBU08sSUFBSSxDQUFDc0QsR0FBRyxDQUFDLENBQUN4QixLQUFLeUIsc0JBQ3ZCLDhEQUFDNUUsNENBQUtBOzRDQUFheUUsU0FBUTs0Q0FBWWIsV0FBVTs7Z0RBQzlDVDs4REFDRCw4REFBQzBCO29EQUNDUCxNQUFLO29EQUNMRSxTQUFTLElBQU14QixVQUFVRztvREFDekJTLFdBQVU7OERBRVYsNEVBQUN2RCw0RkFBQ0E7d0RBQUN1RCxXQUFVOzs7Ozs7Ozs7Ozs7MkNBUExnQjs7Ozs7Ozs7Ozs7Ozs7Ozt3QkFjbkJ0RCxPQUFPbUIsTUFBTSxrQkFDWiw4REFBQ3NCOzRCQUFJSCxXQUFVO3NDQUNiLDRFQUFDUTtnQ0FBRVIsV0FBVTswQ0FBNEJ0QyxPQUFPbUIsTUFBTTs7Ozs7Ozs7Ozs7c0NBRzFELDhEQUFDbEQsb0RBQVlBOzRCQUFDcUUsV0FBVTs7OENBQ3RCLDhEQUFDcEUsOENBQU1BO29DQUFDOEUsTUFBSztvQ0FBU0csU0FBUTtvQ0FBVUQsU0FBU2hFO29DQUFTc0UsVUFBVWxFOzhDQUFTOzs7Ozs7OENBRzdFLDhEQUFDcEIsOENBQU1BO29DQUFDOEUsTUFBSztvQ0FBU1EsVUFBVWxFOzhDQUM3QkEsVUFBVSxjQUFjSCxPQUFPLGdCQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPOUQ7R0EvUHdCSDs7UUFDVXBCLG9EQUFZQTs7O0tBRHRCb0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vY29tcG9uZW50cy9WZWN0YWxUYXNrTW9kYWwuanM/YTgyZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVRhc2tTdG9yZSB9IGZyb20gJy4uL2xpYi9zdG9yZSc7XG5pbXBvcnQgeyBEaWFsb2csIERpYWxvZ0NvbnRlbnQsIERpYWxvZ0hlYWRlciwgRGlhbG9nVGl0bGUsIERpYWxvZ0Zvb3RlciB9IGZyb20gJy4vdWkvZGlhbG9nJztcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJy4vdWkvYnV0dG9uJztcbmltcG9ydCB7IElucHV0IH0gZnJvbSAnLi91aS9pbnB1dCc7XG5pbXBvcnQgeyBUZXh0YXJlYSB9IGZyb20gJy4vdWkvdGV4dGFyZWEnO1xuaW1wb3J0IHsgU2VsZWN0LCBTZWxlY3RDb250ZW50LCBTZWxlY3RJdGVtLCBTZWxlY3RUcmlnZ2VyLCBTZWxlY3RWYWx1ZSB9IGZyb20gJy4vdWkvc2VsZWN0JztcbmltcG9ydCB7IEJhZGdlIH0gZnJvbSAnLi91aS9iYWRnZSc7XG5pbXBvcnQgeyBDYWxlbmRhciwgQ2xvY2ssIEZsYWcsIFRhZywgWCB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBWZWN0YWxUYXNrTW9kYWwoeyBpc09wZW4sIG9uQ2xvc2UsIHRhc2sgPSBudWxsIH0pIHtcbiAgY29uc3QgeyBhZGRUYXNrLCB1cGRhdGVUYXNrIH0gPSB1c2VUYXNrU3RvcmUoKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbZm9ybURhdGEsIHNldEZvcm1EYXRhXSA9IHVzZVN0YXRlKHtcbiAgICB0aXRsZTogdGFzaz8udGl0bGUgfHwgJycsXG4gICAgZGVzY3JpcHRpb246IHRhc2s/LmRlc2NyaXB0aW9uIHx8ICcnLFxuICAgIHN0YXR1czogdGFzaz8uc3RhdHVzIHx8ICd0b2RvJyxcbiAgICBwcmlvcml0eTogdGFzaz8ucHJpb3JpdHkgfHwgJ21lZGl1bScsXG4gICAgZHVlRGF0ZTogdGFzaz8uZHVlRGF0ZSB8fCAnJyxcbiAgICB0YWdzOiB0YXNrPy50YWdzIHx8IFtdXG4gIH0pO1xuICBjb25zdCBbZXJyb3JzLCBzZXRFcnJvcnNdID0gdXNlU3RhdGUoe30pO1xuICBjb25zdCBbbmV3VGFnLCBzZXROZXdUYWddID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCB2YWxpZGF0ZUZvcm0gPSAoKSA9PiB7XG4gICAgY29uc3QgbmV3RXJyb3JzID0ge307XG4gICAgaWYgKCFmb3JtRGF0YS50aXRsZS50cmltKCkpIHtcbiAgICAgIG5ld0Vycm9ycy50aXRsZSA9ICdUYXNrIHRpdGxlIGlzIHJlcXVpcmVkJztcbiAgICB9XG4gICAgaWYgKGZvcm1EYXRhLnRpdGxlLmxlbmd0aCA+IDEwMCkge1xuICAgICAgbmV3RXJyb3JzLnRpdGxlID0gJ1Rhc2sgdGl0bGUgbXVzdCBiZSBsZXNzIHRoYW4gMTAwIGNoYXJhY3RlcnMnO1xuICAgIH1cbiAgICBpZiAoZm9ybURhdGEuZGVzY3JpcHRpb24ubGVuZ3RoID4gNTAwKSB7XG4gICAgICBuZXdFcnJvcnMuZGVzY3JpcHRpb24gPSAnRGVzY3JpcHRpb24gbXVzdCBiZSBsZXNzIHRoYW4gNTAwIGNoYXJhY3RlcnMnO1xuICAgIH1cbiAgICBzZXRFcnJvcnMobmV3RXJyb3JzKTtcbiAgICByZXR1cm4gT2JqZWN0LmtleXMobmV3RXJyb3JzKS5sZW5ndGggPT09IDA7XG4gIH07XG4gIGNvbnN0IGhhbmRsZVN1Ym1pdCA9IGFzeW5jIChlKSA9PiB7XG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIGlmICghdmFsaWRhdGVGb3JtKCkpIHJldHVybjtcbiAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZygnU3VibWl0dGluZyB0YXNrIHdpdGggZGF0YTonLCBmb3JtRGF0YSk7XG4gICAgICBpZiAodGFzaykge1xuICAgICAgICBhd2FpdCB1cGRhdGVUYXNrKHRhc2suaWQsIGZvcm1EYXRhKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGF3YWl0IGFkZFRhc2soZm9ybURhdGEpO1xuICAgICAgfVxuICAgICAgb25DbG9zZSgpO1xuICAgICAgc2V0Rm9ybURhdGEoe1xuICAgICAgICB0aXRsZTogJycsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnJyxcbiAgICAgICAgc3RhdHVzOiAndG9kbycsXG4gICAgICAgIHByaW9yaXR5OiAnbWVkaXVtJyxcbiAgICAgICAgZHVlRGF0ZTogJycsXG4gICAgICAgIHRhZ3M6IFtdXG4gICAgICB9KTtcbiAgICAgIHNldEVycm9ycyh7fSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBzYXZlIHRhc2s6JywgZXJyb3IpO1xuICAgICAgbGV0IGVycm9yTWVzc2FnZSA9ICdGYWlsZWQgdG8gc2F2ZSB0YXNrLiBQbGVhc2UgdHJ5IGFnYWluLic7XG4gICAgICBpZiAoZXJyb3IubWVzc2FnZSA9PT0gJ1VzZXIgbm90IGF1dGhlbnRpY2F0ZWQnKSB7XG4gICAgICAgIGVycm9yTWVzc2FnZSA9ICdQbGVhc2UgbG9nIGluIHRvIGNyZWF0ZSB0YXNrcy4nO1xuICAgICAgfSBlbHNlIGlmIChlcnJvci5tZXNzYWdlKSB7XG4gICAgICAgIGVycm9yTWVzc2FnZSA9IGVycm9yLm1lc3NhZ2U7XG4gICAgICB9XG4gICAgICBzZXRFcnJvcnMoeyBzdWJtaXQ6IGVycm9yTWVzc2FnZSB9KTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuICBjb25zdCBoYW5kbGVJbnB1dENoYW5nZSA9IChmaWVsZCwgdmFsdWUpID0+IHtcbiAgICBzZXRGb3JtRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIFtmaWVsZF06IHZhbHVlIH0pKTtcbiAgICBpZiAoZXJyb3JzW2ZpZWxkXSkge1xuICAgICAgc2V0RXJyb3JzKHByZXYgPT4gKHsgLi4ucHJldiwgW2ZpZWxkXTogJycgfSkpO1xuICAgIH1cbiAgfTtcbiAgY29uc3QgYWRkVGFnID0gKCkgPT4ge1xuICAgIGlmIChuZXdUYWcudHJpbSgpICYmICFmb3JtRGF0YS50YWdzLmluY2x1ZGVzKG5ld1RhZy50cmltKCkpKSB7XG4gICAgICBzZXRGb3JtRGF0YShwcmV2ID0+ICh7XG4gICAgICAgIC4uLnByZXYsXG4gICAgICAgIHRhZ3M6IFsuLi5wcmV2LnRhZ3MsIG5ld1RhZy50cmltKCldXG4gICAgICB9KSk7XG4gICAgICBzZXROZXdUYWcoJycpO1xuICAgIH1cbiAgfTtcbiAgY29uc3QgcmVtb3ZlVGFnID0gKHRhZ1RvUmVtb3ZlKSA9PiB7XG4gICAgc2V0Rm9ybURhdGEocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIHRhZ3M6IHByZXYudGFncy5maWx0ZXIodGFnID0+IHRhZyAhPT0gdGFnVG9SZW1vdmUpXG4gICAgfSkpO1xuICB9O1xuICBjb25zdCBoYW5kbGVLZXlQcmVzcyA9IChlKSA9PiB7XG4gICAgaWYgKGUua2V5ID09PSAnRW50ZXInICYmIGUudGFyZ2V0Lm5hbWUgPT09ICduZXdUYWcnKSB7XG4gICAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgICBhZGRUYWcoKTtcbiAgICB9XG4gIH07XG4gIGNvbnN0IGdldFByaW9yaXR5Q29sb3IgPSAocHJpb3JpdHkpID0+IHtcbiAgICBzd2l0Y2ggKHByaW9yaXR5KSB7XG4gICAgICBjYXNlICdoaWdoJzogcmV0dXJuICdkZXN0cnVjdGl2ZSc7XG4gICAgICBjYXNlICdtZWRpdW0nOiByZXR1cm4gJ3dhcm5pbmcnO1xuICAgICAgY2FzZSAnbG93JzogcmV0dXJuICdzdWNjZXNzJztcbiAgICAgIGRlZmF1bHQ6IHJldHVybiAnc2Vjb25kYXJ5JztcbiAgICB9XG4gIH07XG4gIGNvbnN0IGdldFN0YXR1c0xhYmVsID0gKHN0YXR1cykgPT4ge1xuICAgIHN3aXRjaCAoc3RhdHVzKSB7XG4gICAgICBjYXNlICd0b2RvJzogcmV0dXJuICdUbyBEbyc7XG4gICAgICBjYXNlICdpbnByb2dyZXNzJzogcmV0dXJuICdJbiBQcm9ncmVzcyc7XG4gICAgICBjYXNlICdkb25lJzogcmV0dXJuICdEb25lJztcbiAgICAgIGRlZmF1bHQ6IHJldHVybiBzdGF0dXM7XG4gICAgfVxuICB9O1xuICByZXR1cm4gKFxuICAgIDxEaWFsb2cgb3Blbj17aXNPcGVufSBvbk9wZW5DaGFuZ2U9e29uQ2xvc2V9PlxuICAgICAgPERpYWxvZ0NvbnRlbnQgY2xhc3NOYW1lPVwic206bWF4LXctWzUwMHB4XSBtYXgtaC1bOTB2aF0gb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgIDxEaWFsb2dIZWFkZXI+XG4gICAgICAgICAgPERpYWxvZ1RpdGxlIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICA8VGFnIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1wcmltYXJ5XCIgLz5cbiAgICAgICAgICAgIHt0YXNrID8gJ0VkaXQgVGFzaycgOiAnQ3JlYXRlIE5ldyBUYXNrJ31cbiAgICAgICAgICA8L0RpYWxvZ1RpdGxlPlxuICAgICAgICA8L0RpYWxvZ0hlYWRlcj5cbiAgICAgICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZVN1Ym1pdH0gY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwidGl0bGVcIiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgICBUYXNrIFRpdGxlICpcbiAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgaWQ9XCJ0aXRsZVwiXG4gICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS50aXRsZX1cbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVJbnB1dENoYW5nZSgndGl0bGUnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgdGFzayB0aXRsZS4uLlwiXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17ZXJyb3JzLnRpdGxlID8gJ2JvcmRlci1kZXN0cnVjdGl2ZSBmb2N1czpib3JkZXItZGVzdHJ1Y3RpdmUnIDogJyd9XG4gICAgICAgICAgICAvPlxuICAgICAgICAgICAge2Vycm9ycy50aXRsZSAmJiAoXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1kZXN0cnVjdGl2ZVwiPntlcnJvcnMudGl0bGV9PC9wPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwic3RhdHVzXCIgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICBTdGF0dXNcbiAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgPFNlbGVjdCB2YWx1ZT17Zm9ybURhdGEuc3RhdHVzfSBvblZhbHVlQ2hhbmdlPXsodmFsdWUpID0+IGhhbmRsZUlucHV0Q2hhbmdlKCdzdGF0dXMnLCB2YWx1ZSl9PlxuICAgICAgICAgICAgICAgIDxTZWxlY3RUcmlnZ2VyPlxuICAgICAgICAgICAgICAgICAgPFNlbGVjdFZhbHVlIC8+XG4gICAgICAgICAgICAgICAgPC9TZWxlY3RUcmlnZ2VyPlxuICAgICAgICAgICAgICAgIDxTZWxlY3RDb250ZW50PlxuICAgICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0gdmFsdWU9XCJ0b2RvXCI+VG8gRG88L1NlbGVjdEl0ZW0+XG4gICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cImlucHJvZ3Jlc3NcIj5JbiBQcm9ncmVzczwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwiZG9uZVwiPkRvbmU8L1NlbGVjdEl0ZW0+XG4gICAgICAgICAgICAgICAgPC9TZWxlY3RDb250ZW50PlxuICAgICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJwcmlvcml0eVwiIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgUHJpb3JpdHlcbiAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgPFNlbGVjdCB2YWx1ZT17Zm9ybURhdGEucHJpb3JpdHl9IG9uVmFsdWVDaGFuZ2U9eyh2YWx1ZSkgPT4gaGFuZGxlSW5wdXRDaGFuZ2UoJ3ByaW9yaXR5JywgdmFsdWUpfT5cbiAgICAgICAgICAgICAgICA8U2VsZWN0VHJpZ2dlcj5cbiAgICAgICAgICAgICAgICAgIDxTZWxlY3RWYWx1ZSAvPlxuICAgICAgICAgICAgICAgIDwvU2VsZWN0VHJpZ2dlcj5cbiAgICAgICAgICAgICAgICA8U2VsZWN0Q29udGVudD5cbiAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwiaGlnaFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPEZsYWcgY2xhc3NOYW1lPVwiaC0zIHctMyB0ZXh0LWRlc3RydWN0aXZlXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICBIaWdoIFByaW9yaXR5XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9TZWxlY3RJdGVtPlxuICAgICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0gdmFsdWU9XCJtZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxGbGFnIGNsYXNzTmFtZT1cImgtMyB3LTMgdGV4dC13YXJuaW5nXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICBNZWRpdW0gUHJpb3JpdHlcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L1NlbGVjdEl0ZW0+XG4gICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cImxvd1wiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPEZsYWcgY2xhc3NOYW1lPVwiaC0zIHctMyB0ZXh0LXN1Y2Nlc3NcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIExvdyBQcmlvcml0eVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICA8L1NlbGVjdENvbnRlbnQ+XG4gICAgICAgICAgICAgIDwvU2VsZWN0PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwiZHVlRGF0ZVwiIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgIER1ZSBEYXRlXG4gICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICBpZD1cImR1ZURhdGVcIlxuICAgICAgICAgICAgICAgIHR5cGU9XCJkYXRlXCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuZHVlRGF0ZX1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUlucHV0Q2hhbmdlKCdkdWVEYXRlJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInBsLTEwXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPENhbGVuZGFyIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMyB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIGgtNCB3LTQgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCIgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICA8bGFiZWwgaHRtbEZvcj1cImRlc2NyaXB0aW9uXCIgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgRGVzY3JpcHRpb25cbiAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICA8VGV4dGFyZWFcbiAgICAgICAgICAgICAgaWQ9XCJkZXNjcmlwdGlvblwiXG4gICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVJbnB1dENoYW5nZSgnZGVzY3JpcHRpb24nLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiQWRkIHRhc2sgZGVzY3JpcHRpb24uLi5cIlxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2BtaW4taC1bMTAwcHhdICR7ZXJyb3JzLmRlc2NyaXB0aW9uID8gJ2JvcmRlci1kZXN0cnVjdGl2ZSBmb2N1czpib3JkZXItZGVzdHJ1Y3RpdmUnIDogJyd9YH1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICB7ZXJyb3JzLmRlc2NyaXB0aW9uICYmIChcbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWRlc3RydWN0aXZlXCI+e2Vycm9ycy5kZXNjcmlwdGlvbn08L3A+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWZvcmVncm91bmRcIj5UYWdzPC9sYWJlbD5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMlwiPlxuICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICBuYW1lPVwibmV3VGFnXCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17bmV3VGFnfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TmV3VGFnKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICBvbktleVByZXNzPXtoYW5kbGVLZXlQcmVzc31cbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkFkZCB0YWcuLi5cIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMVwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDxCdXR0b24gdHlwZT1cImJ1dHRvblwiIG9uQ2xpY2s9e2FkZFRhZ30gdmFyaWFudD1cIm91dGxpbmVcIiBzaXplPVwic21cIj5cbiAgICAgICAgICAgICAgICBBZGRcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIHtmb3JtRGF0YS50YWdzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC0yIG10LTJcIj5cbiAgICAgICAgICAgICAgICB7Zm9ybURhdGEudGFncy5tYXAoKHRhZywgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgIDxCYWRnZSBrZXk9e2luZGV4fSB2YXJpYW50PVwic2Vjb25kYXJ5XCIgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTFcIj5cbiAgICAgICAgICAgICAgICAgICAge3RhZ31cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJlbW92ZVRhZyh0YWcpfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1sLTEgaG92ZXI6dGV4dC1kZXN0cnVjdGl2ZVwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8WCBjbGFzc05hbWU9XCJoLTMgdy0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAge2Vycm9ycy5zdWJtaXQgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTMgYmctZGVzdHJ1Y3RpdmUvMTAgYm9yZGVyIGJvcmRlci1kZXN0cnVjdGl2ZS8yMCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1kZXN0cnVjdGl2ZVwiPntlcnJvcnMuc3VibWl0fTwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgICAgPERpYWxvZ0Zvb3RlciBjbGFzc05hbWU9XCJnYXAtMlwiPlxuICAgICAgICAgICAgPEJ1dHRvbiB0eXBlPVwiYnV0dG9uXCIgdmFyaWFudD1cIm91dGxpbmVcIiBvbkNsaWNrPXtvbkNsb3NlfSBkaXNhYmxlZD17bG9hZGluZ30+XG4gICAgICAgICAgICAgIENhbmNlbFxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8QnV0dG9uIHR5cGU9XCJzdWJtaXRcIiBkaXNhYmxlZD17bG9hZGluZ30+XG4gICAgICAgICAgICAgIHtsb2FkaW5nID8gJ1NhdmluZy4uLicgOiB0YXNrID8gJ1VwZGF0ZSBUYXNrJyA6ICdDcmVhdGUgVGFzayd9XG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8L0RpYWxvZ0Zvb3Rlcj5cbiAgICAgICAgPC9mb3JtPlxuICAgICAgPC9EaWFsb2dDb250ZW50PlxuICAgIDwvRGlhbG9nPlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlVGFza1N0b3JlIiwiRGlhbG9nIiwiRGlhbG9nQ29udGVudCIsIkRpYWxvZ0hlYWRlciIsIkRpYWxvZ1RpdGxlIiwiRGlhbG9nRm9vdGVyIiwiQnV0dG9uIiwiSW5wdXQiLCJUZXh0YXJlYSIsIlNlbGVjdCIsIlNlbGVjdENvbnRlbnQiLCJTZWxlY3RJdGVtIiwiU2VsZWN0VHJpZ2dlciIsIlNlbGVjdFZhbHVlIiwiQmFkZ2UiLCJDYWxlbmRhciIsIkNsb2NrIiwiRmxhZyIsIlRhZyIsIlgiLCJWZWN0YWxUYXNrTW9kYWwiLCJpc09wZW4iLCJvbkNsb3NlIiwidGFzayIsImFkZFRhc2siLCJ1cGRhdGVUYXNrIiwibG9hZGluZyIsInNldExvYWRpbmciLCJmb3JtRGF0YSIsInNldEZvcm1EYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsInN0YXR1cyIsInByaW9yaXR5IiwiZHVlRGF0ZSIsInRhZ3MiLCJlcnJvcnMiLCJzZXRFcnJvcnMiLCJuZXdUYWciLCJzZXROZXdUYWciLCJ2YWxpZGF0ZUZvcm0iLCJuZXdFcnJvcnMiLCJ0cmltIiwibGVuZ3RoIiwiT2JqZWN0Iiwia2V5cyIsImhhbmRsZVN1Ym1pdCIsImUiLCJwcmV2ZW50RGVmYXVsdCIsImNvbnNvbGUiLCJsb2ciLCJpZCIsImVycm9yIiwiZXJyb3JNZXNzYWdlIiwibWVzc2FnZSIsInN1Ym1pdCIsImhhbmRsZUlucHV0Q2hhbmdlIiwiZmllbGQiLCJ2YWx1ZSIsInByZXYiLCJhZGRUYWciLCJpbmNsdWRlcyIsInJlbW92ZVRhZyIsInRhZ1RvUmVtb3ZlIiwiZmlsdGVyIiwidGFnIiwiaGFuZGxlS2V5UHJlc3MiLCJrZXkiLCJ0YXJnZXQiLCJuYW1lIiwiZ2V0UHJpb3JpdHlDb2xvciIsImdldFN0YXR1c0xhYmVsIiwib3BlbiIsIm9uT3BlbkNoYW5nZSIsImNsYXNzTmFtZSIsImZvcm0iLCJvblN1Ym1pdCIsImRpdiIsImxhYmVsIiwiaHRtbEZvciIsIm9uQ2hhbmdlIiwicGxhY2Vob2xkZXIiLCJwIiwib25WYWx1ZUNoYW5nZSIsInR5cGUiLCJvbktleVByZXNzIiwib25DbGljayIsInZhcmlhbnQiLCJzaXplIiwibWFwIiwiaW5kZXgiLCJidXR0b24iLCJkaXNhYmxlZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/VectalTaskModal.js\n"));

/***/ })

});