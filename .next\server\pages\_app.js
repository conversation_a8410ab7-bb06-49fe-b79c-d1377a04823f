/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_app";
exports.ids = ["pages/_app"];
exports.modules = {

/***/ "./contexts/AuthContext.js":
/*!*********************************!*\
  !*** ./contexts/AuthContext.js ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"firebase/auth\");\n/* harmony import */ var _lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/firebase-config */ \"./lib/firebase-config.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/store */ \"./lib/store.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([firebase_auth__WEBPACK_IMPORTED_MODULE_2__, _lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__, _lib_store__WEBPACK_IMPORTED_MODULE_4__]);\n([firebase_auth__WEBPACK_IMPORTED_MODULE_2__, _lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__, _lib_store__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({});\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const { setUser: setStoreUser, loadTasks } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_4__.useTaskStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!_lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__.auth) {\n            console.log(\"Auth not initialized\");\n            setLoading(false);\n            return;\n        }\n        console.log(\"Setting up auth state listener\");\n        const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.onAuthStateChanged)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__.auth, async (user)=>{\n            console.log(\"Auth state changed:\", user ? \"User logged in\" : \"User logged out\");\n            console.log(\"User object:\", user);\n            setUser(user);\n            setStoreUser(user);\n            if (user) {\n                console.log(\"Loading tasks for user:\", user.uid);\n                await loadTasks(user.uid);\n            }\n            setLoading(false);\n        });\n        return unsubscribe;\n    }, [\n        setStoreUser,\n        loadTasks\n    ]);\n    const signup = async (email, password, displayName)=>{\n        const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.createUserWithEmailAndPassword)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__.auth, email, password);\n        await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.updateProfile)(userCredential.user, {\n            displayName\n        });\n        return userCredential;\n    };\n    const login = async (email, password)=>{\n        return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signInWithEmailAndPassword)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__.auth, email, password);\n    };\n    const logout = async ()=>{\n        return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signOut)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__.auth);\n    };\n    const value = {\n        user,\n        signup,\n        login,\n        logout,\n        loading\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\contexts\\\\AuthContext.js\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, undefined);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./contexts/AuthContext.js\n");

/***/ }),

/***/ "./contexts/ThemeContext.js":
/*!**********************************!*\
  !*** ./contexts/ThemeContext.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({});\nconst useTheme = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (!context) {\n        throw new Error(\"useTheme must be used within a ThemeProvider\");\n    }\n    return context;\n};\nconst ThemeProvider = ({ children })=>{\n    const [isDarkMode, setIsDarkMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedTheme = localStorage.getItem(\"theme\");\n        const prefersDark = window.matchMedia(\"(prefers-color-scheme: dark)\").matches;\n        const shouldUseDark = savedTheme === \"dark\" || !savedTheme && prefersDark;\n        setIsDarkMode(shouldUseDark);\n        if (shouldUseDark) {\n            document.documentElement.classList.add(\"dark\");\n        } else {\n            document.documentElement.classList.remove(\"dark\");\n        }\n        setIsLoaded(true);\n    }, []);\n    const toggleTheme = ()=>{\n        const newTheme = !isDarkMode;\n        setIsDarkMode(newTheme);\n        if (newTheme) {\n            document.documentElement.classList.add(\"dark\");\n            localStorage.setItem(\"theme\", \"dark\");\n        } else {\n            document.documentElement.classList.remove(\"dark\");\n            localStorage.setItem(\"theme\", \"light\");\n        }\n    };\n    const value = {\n        isDarkMode,\n        toggleTheme,\n        isLoaded\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\contexts\\\\ThemeContext.js\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb250ZXh0cy9UaGVtZUNvbnRleHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUF1RTtBQUV2RSxNQUFNSSw2QkFBZUosb0RBQWFBLENBQUMsQ0FBQztBQUU3QixNQUFNSyxXQUFXO0lBQ3RCLE1BQU1DLFVBQVVMLGlEQUFVQSxDQUFDRztJQUMzQixJQUFJLENBQUNFLFNBQVM7UUFDWixNQUFNLElBQUlDLE1BQU07SUFDbEI7SUFDQSxPQUFPRDtBQUNULEVBQUU7QUFFSyxNQUFNRSxnQkFBZ0IsQ0FBQyxFQUFFQyxRQUFRLEVBQUU7SUFDeEMsTUFBTSxDQUFDQyxZQUFZQyxjQUFjLEdBQUdSLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ1MsVUFBVUMsWUFBWSxHQUFHViwrQ0FBUUEsQ0FBQztJQUV6Q0QsZ0RBQVNBLENBQUM7UUFDUixNQUFNWSxhQUFhQyxhQUFhQyxPQUFPLENBQUM7UUFDeEMsTUFBTUMsY0FBY0MsT0FBT0MsVUFBVSxDQUFDLGdDQUFnQ0MsT0FBTztRQUU3RSxNQUFNQyxnQkFBZ0JQLGVBQWUsVUFBVyxDQUFDQSxjQUFjRztRQUMvRE4sY0FBY1U7UUFFZCxJQUFJQSxlQUFlO1lBQ2pCQyxTQUFTQyxlQUFlLENBQUNDLFNBQVMsQ0FBQ0MsR0FBRyxDQUFDO1FBQ3pDLE9BQU87WUFDTEgsU0FBU0MsZUFBZSxDQUFDQyxTQUFTLENBQUNFLE1BQU0sQ0FBQztRQUM1QztRQUVBYixZQUFZO0lBQ2QsR0FBRyxFQUFFO0lBRUwsTUFBTWMsY0FBYztRQUNsQixNQUFNQyxXQUFXLENBQUNsQjtRQUNsQkMsY0FBY2lCO1FBRWQsSUFBSUEsVUFBVTtZQUNaTixTQUFTQyxlQUFlLENBQUNDLFNBQVMsQ0FBQ0MsR0FBRyxDQUFDO1lBQ3ZDVixhQUFhYyxPQUFPLENBQUMsU0FBUztRQUNoQyxPQUFPO1lBQ0xQLFNBQVNDLGVBQWUsQ0FBQ0MsU0FBUyxDQUFDRSxNQUFNLENBQUM7WUFDMUNYLGFBQWFjLE9BQU8sQ0FBQyxTQUFTO1FBQ2hDO0lBQ0Y7SUFFQSxNQUFNQyxRQUFRO1FBQ1pwQjtRQUNBaUI7UUFDQWY7SUFDRjtJQUVBLHFCQUNFLDhEQUFDUixhQUFhMkIsUUFBUTtRQUFDRCxPQUFPQTtrQkFDM0JyQjs7Ozs7O0FBR1AsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL3Rhc2stbWFuYWdlbWVudC1hcHAvLi9jb250ZXh0cy9UaGVtZUNvbnRleHQuanM/YjczOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuXG5jb25zdCBUaGVtZUNvbnRleHQgPSBjcmVhdGVDb250ZXh0KHt9KTtcblxuZXhwb3J0IGNvbnN0IHVzZVRoZW1lID0gKCkgPT4ge1xuICBjb25zdCBjb250ZXh0ID0gdXNlQ29udGV4dChUaGVtZUNvbnRleHQpO1xuICBpZiAoIWNvbnRleHQpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ3VzZVRoZW1lIG11c3QgYmUgdXNlZCB3aXRoaW4gYSBUaGVtZVByb3ZpZGVyJyk7XG4gIH1cbiAgcmV0dXJuIGNvbnRleHQ7XG59O1xuXG5leHBvcnQgY29uc3QgVGhlbWVQcm92aWRlciA9ICh7IGNoaWxkcmVuIH0pID0+IHtcbiAgY29uc3QgW2lzRGFya01vZGUsIHNldElzRGFya01vZGVdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbaXNMb2FkZWQsIHNldElzTG9hZGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IHNhdmVkVGhlbWUgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndGhlbWUnKTtcbiAgICBjb25zdCBwcmVmZXJzRGFyayA9IHdpbmRvdy5tYXRjaE1lZGlhKCcocHJlZmVycy1jb2xvci1zY2hlbWU6IGRhcmspJykubWF0Y2hlcztcbiAgICBcbiAgICBjb25zdCBzaG91bGRVc2VEYXJrID0gc2F2ZWRUaGVtZSA9PT0gJ2RhcmsnIHx8ICghc2F2ZWRUaGVtZSAmJiBwcmVmZXJzRGFyayk7XG4gICAgc2V0SXNEYXJrTW9kZShzaG91bGRVc2VEYXJrKTtcbiAgICBcbiAgICBpZiAoc2hvdWxkVXNlRGFyaykge1xuICAgICAgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LmNsYXNzTGlzdC5hZGQoJ2RhcmsnKTtcbiAgICB9IGVsc2Uge1xuICAgICAgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LmNsYXNzTGlzdC5yZW1vdmUoJ2RhcmsnKTtcbiAgICB9XG4gICAgXG4gICAgc2V0SXNMb2FkZWQodHJ1ZSk7XG4gIH0sIFtdKTtcblxuICBjb25zdCB0b2dnbGVUaGVtZSA9ICgpID0+IHtcbiAgICBjb25zdCBuZXdUaGVtZSA9ICFpc0RhcmtNb2RlO1xuICAgIHNldElzRGFya01vZGUobmV3VGhlbWUpO1xuICAgIFxuICAgIGlmIChuZXdUaGVtZSkge1xuICAgICAgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LmNsYXNzTGlzdC5hZGQoJ2RhcmsnKTtcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCd0aGVtZScsICdkYXJrJyk7XG4gICAgfSBlbHNlIHtcbiAgICAgIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5jbGFzc0xpc3QucmVtb3ZlKCdkYXJrJyk7XG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgndGhlbWUnLCAnbGlnaHQnKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgdmFsdWUgPSB7XG4gICAgaXNEYXJrTW9kZSxcbiAgICB0b2dnbGVUaGVtZSxcbiAgICBpc0xvYWRlZFxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPFRoZW1lQ29udGV4dC5Qcm92aWRlciB2YWx1ZT17dmFsdWV9PlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvVGhlbWVDb250ZXh0LlByb3ZpZGVyPlxuICApO1xufTtcbiJdLCJuYW1lcyI6WyJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZUVmZmVjdCIsInVzZVN0YXRlIiwiVGhlbWVDb250ZXh0IiwidXNlVGhlbWUiLCJjb250ZXh0IiwiRXJyb3IiLCJUaGVtZVByb3ZpZGVyIiwiY2hpbGRyZW4iLCJpc0RhcmtNb2RlIiwic2V0SXNEYXJrTW9kZSIsImlzTG9hZGVkIiwic2V0SXNMb2FkZWQiLCJzYXZlZFRoZW1lIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsInByZWZlcnNEYXJrIiwid2luZG93IiwibWF0Y2hNZWRpYSIsIm1hdGNoZXMiLCJzaG91bGRVc2VEYXJrIiwiZG9jdW1lbnQiLCJkb2N1bWVudEVsZW1lbnQiLCJjbGFzc0xpc3QiLCJhZGQiLCJyZW1vdmUiLCJ0b2dnbGVUaGVtZSIsIm5ld1RoZW1lIiwic2V0SXRlbSIsInZhbHVlIiwiUHJvdmlkZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./contexts/ThemeContext.js\n");

/***/ }),

/***/ "./lib/firebase-config.js":
/*!********************************!*\
  !*** ./lib/firebase-config.js ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"firebase/app\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"firebase/auth\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"firebase/firestore\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([firebase_app__WEBPACK_IMPORTED_MODULE_0__, firebase_auth__WEBPACK_IMPORTED_MODULE_1__, firebase_firestore__WEBPACK_IMPORTED_MODULE_2__]);\n([firebase_app__WEBPACK_IMPORTED_MODULE_0__, firebase_auth__WEBPACK_IMPORTED_MODULE_1__, firebase_firestore__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst firebaseConfig = {\n    apiKey: \"AIzaSyAvL0q2Bz4ZLNPVGJjo2gNMEDddra87odQ\" || 0,\n    authDomain: \"zatconss.firebaseapp.com\" || 0,\n    databaseURL: \"https://zatconss-default-rtdb.firebaseio.com\",\n    projectId: \"zatconss\" || 0,\n    storageBucket: \"zatconss.firebasestorage.app\" || 0,\n    messagingSenderId: \"947257597349\" || 0,\n    appId: \"1:947257597349:web:4f62c8e2bf4952eebe5c4c\" || 0,\n    measurementId: \"G-ZCHBDYX3VW\"\n};\nlet app;\nlet auth;\nlet db;\ntry {\n    // Check if Firebase app is already initialized\n    app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)().length === 0 ? (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig) : (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)()[0];\n    auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.getAuth)(app);\n    db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getFirestore)(app);\n    console.log(\"Firebase initialized successfully\");\n    console.log(\"Project ID:\", firebaseConfig.projectId);\n    console.log(\"Auth domain:\", firebaseConfig.authDomain);\n    if ( true && firebaseConfig.apiKey === \"demo-key\") {\n        console.warn(\"⚠️  Using demo Firebase config. Please set up your Firebase project and update .env.local\");\n    }\n} catch (error) {\n    console.error(\"Firebase initialization error:\", error);\n    console.log(\"\\uD83D\\uDCDD Please check your Firebase configuration in .env.local\");\n    console.log(\"Config being used:\", firebaseConfig);\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/firebase-config.js\n");

/***/ }),

/***/ "./lib/firebase-utils.js":
/*!*******************************!*\
  !*** ./lib/firebase-utils.js ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTask: () => (/* binding */ createTask),\n/* harmony export */   deleteTask: () => (/* binding */ deleteTask),\n/* harmony export */   getUserTasks: () => (/* binding */ getUserTasks),\n/* harmony export */   updateTask: () => (/* binding */ updateTask)\n/* harmony export */ });\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/firestore */ \"firebase/firestore\");\n/* harmony import */ var _firebase_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase-config */ \"./lib/firebase-config.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([firebase_firestore__WEBPACK_IMPORTED_MODULE_0__, _firebase_config__WEBPACK_IMPORTED_MODULE_1__]);\n([firebase_firestore__WEBPACK_IMPORTED_MODULE_0__, _firebase_config__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nconst createTask = async (userId, taskData)=>{\n    try {\n        console.log(\"Creating task in Firebase with userId:\", userId);\n        console.log(\"Task data:\", taskData);\n        if (!_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db) {\n            throw new Error(\"Firebase database not initialized\");\n        }\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, \"tasks\"), {\n            ...taskData,\n            userId,\n            createdAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.serverTimestamp)(),\n            updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.serverTimestamp)()\n        });\n        const newTask = {\n            id: docRef.id,\n            ...taskData\n        };\n        console.log(\"Task created successfully in Firebase:\", newTask);\n        return newTask;\n    } catch (error) {\n        console.error(\"Error creating task in Firebase:\", error);\n        throw error;\n    }\n};\nconst updateTask = async (taskId, updates)=>{\n    try {\n        const taskRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, \"tasks\", taskId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(taskRef, {\n            ...updates,\n            updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.serverTimestamp)()\n        });\n        return {\n            id: taskId,\n            ...updates\n        };\n    } catch (error) {\n        console.error(\"Error updating task:\", error);\n        throw error;\n    }\n};\nconst deleteTask = async (taskId)=>{\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, \"tasks\", taskId));\n        return taskId;\n    } catch (error) {\n        console.error(\"Error deleting task:\", error);\n        throw error;\n    }\n};\nconst getUserTasks = async (userId)=>{\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, \"tasks\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(\"userId\", \"==\", userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(\"createdAt\", \"desc\"));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const tasks = [];\n        querySnapshot.forEach((doc)=>{\n            tasks.push({\n                id: doc.id,\n                ...doc.data()\n            });\n        });\n        return tasks;\n    } catch (error) {\n        console.error(\"Error fetching tasks:\", error);\n        throw error;\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/firebase-utils.js\n");

/***/ }),

/***/ "./lib/store.js":
/*!**********************!*\
  !*** ./lib/store.js ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTaskStore: () => (/* binding */ useTaskStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"zustand\");\n/* harmony import */ var _firebase_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase-utils */ \"./lib/firebase-utils.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([zustand__WEBPACK_IMPORTED_MODULE_0__, _firebase_utils__WEBPACK_IMPORTED_MODULE_1__]);\n([zustand__WEBPACK_IMPORTED_MODULE_0__, _firebase_utils__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nconst useTaskStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        tasks: [],\n        user: null,\n        loading: false,\n        error: null,\n        setUser: (user)=>set({\n                user\n            }),\n        setLoading: (loading)=>set({\n                loading\n            }),\n        setError: (error)=>set({\n                error\n            }),\n        loadTasks: async (userId)=>{\n            set({\n                loading: true,\n                error: null\n            });\n            try {\n                const tasks = await (0,_firebase_utils__WEBPACK_IMPORTED_MODULE_1__.getUserTasks)(userId);\n                set({\n                    tasks,\n                    loading: false\n                });\n            } catch (error) {\n                set({\n                    error: error.message,\n                    loading: false\n                });\n            }\n        },\n        addTask: async (taskData)=>{\n            const { user } = get();\n            console.log(\"addTask called with user:\", user);\n            console.log(\"addTask called with taskData:\", taskData);\n            if (!user) {\n                console.error(\"No user found in store\");\n                throw new Error(\"User not authenticated\");\n            }\n            set({\n                loading: true,\n                error: null\n            });\n            try {\n                const newTask = await (0,_firebase_utils__WEBPACK_IMPORTED_MODULE_1__.createTask)(user.uid, taskData);\n                console.log(\"Task created successfully:\", newTask);\n                set((state)=>({\n                        tasks: [\n                            newTask,\n                            ...state.tasks\n                        ],\n                        loading: false\n                    }));\n                return newTask;\n            } catch (error) {\n                console.error(\"Error in addTask:\", error);\n                set({\n                    error: error.message,\n                    loading: false\n                });\n                throw error;\n            }\n        },\n        updateTask: async (taskId, updates)=>{\n            const { user } = get();\n            if (!user) return;\n            set({\n                loading: true,\n                error: null\n            });\n            try {\n                await (0,_firebase_utils__WEBPACK_IMPORTED_MODULE_1__.updateTask)(taskId, updates);\n                set((state)=>({\n                        tasks: state.tasks.map((task)=>task.id === taskId ? {\n                                ...task,\n                                ...updates\n                            } : task),\n                        loading: false\n                    }));\n            } catch (error) {\n                console.error(\"Error updating task:\", error);\n                set({\n                    error: error.message,\n                    loading: false\n                });\n                throw error;\n            }\n        },\n        deleteTask: async (taskId)=>{\n            const { user } = get();\n            if (!user) return;\n            set({\n                loading: true,\n                error: null\n            });\n            try {\n                await (0,_firebase_utils__WEBPACK_IMPORTED_MODULE_1__.deleteTask)(taskId);\n                set((state)=>({\n                        tasks: state.tasks.filter((task)=>task.id !== taskId),\n                        loading: false\n                    }));\n            } catch (error) {\n                console.error(\"Error deleting task:\", error);\n                set({\n                    error: error.message,\n                    loading: false\n                });\n                throw error;\n            }\n        },\n        moveTask: async (taskId, newColumn)=>{\n            const { updateTask } = get();\n            await updateTask(taskId, {\n                status: newColumn\n            });\n        },\n        getTasksByStatus: (status)=>{\n            const { tasks } = get();\n            return tasks.filter((task)=>task.status === status);\n        },\n        getTaskCount: ()=>{\n            const { tasks } = get();\n            return tasks.length;\n        },\n        findTaskByName: (name)=>{\n            const { tasks } = get();\n            return tasks.find((task)=>task.title.toLowerCase().includes(name.toLowerCase()));\n        }\n    }));\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/store.js\n");

/***/ }),

/***/ "./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../contexts/AuthContext */ \"./contexts/AuthContext.js\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/ThemeContext */ \"./contexts/ThemeContext.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__]);\n_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.ThemeProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\pages\\\\_app.js\",\n                lineNumber: 9,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\pages\\\\_app.js\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\pages\\\\_app.js\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQStCO0FBQ3dCO0FBQ0U7QUFFMUMsU0FBU0UsSUFBSSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBRTtJQUNsRCxxQkFDRSw4REFBQ0gsaUVBQWFBO2tCQUNaLDRFQUFDRCwrREFBWUE7c0JBQ1gsNEVBQUNHO2dCQUFXLEdBQUdDLFNBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJaEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90YXNrLW1hbmFnZW1lbnQtYXBwLy4vcGFnZXMvX2FwcC5qcz9lMGFkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAnLi4vc3R5bGVzL2dsb2JhbHMuY3NzJztcbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gJy4uL2NvbnRleHRzL0F1dGhDb250ZXh0JztcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgfSBmcm9tICcuLi9jb250ZXh0cy9UaGVtZUNvbnRleHQnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHAoeyBDb21wb25lbnQsIHBhZ2VQcm9wcyB9KSB7XG4gIHJldHVybiAoXG4gICAgPFRoZW1lUHJvdmlkZXI+XG4gICAgICA8QXV0aFByb3ZpZGVyPlxuICAgICAgICA8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+XG4gICAgICA8L0F1dGhQcm92aWRlcj5cbiAgICA8L1RoZW1lUHJvdmlkZXI+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiQXV0aFByb3ZpZGVyIiwiVGhlbWVQcm92aWRlciIsIkFwcCIsIkNvbXBvbmVudCIsInBhZ2VQcm9wcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/_app.js\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "firebase/app":
/*!*******************************!*\
  !*** external "firebase/app" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase/app");;

/***/ }),

/***/ "firebase/auth":
/*!********************************!*\
  !*** external "firebase/auth" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase/auth");;

/***/ }),

/***/ "firebase/firestore":
/*!*************************************!*\
  !*** external "firebase/firestore" ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase/firestore");;

/***/ }),

/***/ "zustand":
/*!**************************!*\
  !*** external "zustand" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = import("zustand");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./pages/_app.js"));
module.exports = __webpack_exports__;

})();