"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./lib/firebase-utils.js":
/*!*******************************!*\
  !*** ./lib/firebase-utils.js ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTask: function() { return /* binding */ createTask; },\n/* harmony export */   deleteTask: function() { return /* binding */ deleteTask; },\n/* harmony export */   getUserTasks: function() { return /* binding */ getUserTasks; },\n/* harmony export */   updateTask: function() { return /* binding */ updateTask; }\n/* harmony export */ });\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/firestore */ \"./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var _firebase_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase-config */ \"./lib/firebase-config.js\");\n\n\nconst createTask = async (userId, taskData)=>{\n    try {\n        console.log(\"Creating task in Firebase with userId:\", userId);\n        console.log(\"Task data:\", taskData);\n        if (!_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db) {\n            throw new Error(\"Firebase database not initialized\");\n        }\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, \"tasks\"), {\n            ...taskData,\n            userId,\n            createdAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.serverTimestamp)(),\n            updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.serverTimestamp)()\n        });\n        const newTask = {\n            id: docRef.id,\n            ...taskData\n        };\n        console.log(\"Task created successfully in Firebase:\", newTask);\n        return newTask;\n    } catch (error) {\n        console.error(\"Error creating task in Firebase:\", error);\n        throw error;\n    }\n};\nconst updateTask = async (taskId, updates)=>{\n    try {\n        const taskRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, \"tasks\", taskId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(taskRef, {\n            ...updates,\n            updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.serverTimestamp)()\n        });\n        return {\n            id: taskId,\n            ...updates\n        };\n    } catch (error) {\n        console.error(\"Error updating task:\", error);\n        throw error;\n    }\n};\nconst deleteTask = async (taskId)=>{\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, \"tasks\", taskId));\n        return taskId;\n    } catch (error) {\n        console.error(\"Error deleting task:\", error);\n        throw error;\n    }\n};\nconst getUserTasks = async (userId)=>{\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, \"tasks\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(\"userId\", \"==\", userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(\"createdAt\", \"desc\"));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const tasks = [];\n        querySnapshot.forEach((doc)=>{\n            tasks.push({\n                id: doc.id,\n                ...doc.data()\n            });\n        });\n        return tasks;\n    } catch (error) {\n        console.error(\"Error fetching tasks:\", error);\n        throw error;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/firebase-utils.js\n"));

/***/ })

});