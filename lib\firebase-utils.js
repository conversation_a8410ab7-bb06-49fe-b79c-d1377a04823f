import { 
  collection, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  doc, 
  getDocs, 
  query, 
  where, 
  orderBy,
  serverTimestamp 
} from 'firebase/firestore';
import { db } from './firebase-config';

export const createTask = async (userId, taskData) => {
  try {
    console.log('Creating task in Firebase with userId:', userId);
    console.log('Task data:', taskData);

    if (!db) {
      throw new Error('Firebase database not initialized');
    }

    const docRef = await addDoc(collection(db, 'tasks'), {
      ...taskData,
      userId,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    const newTask = { id: docRef.id, ...taskData };
    console.log('Task created successfully in Firebase:', newTask);
    return newTask;
  } catch (error) {
    console.error('Error creating task in Firebase:', error);
    throw error;
  }
};

export const updateTask = async (taskId, updates) => {
  try {
    const taskRef = doc(db, 'tasks', taskId);
    await updateDoc(taskRef, {
      ...updates,
      updatedAt: serverTimestamp()
    });
    return { id: taskId, ...updates };
  } catch (error) {
    console.error('Error updating task:', error);
    throw error;
  }
};

export const deleteTask = async (taskId) => {
  try {
    await deleteDoc(doc(db, 'tasks', taskId));
    return taskId;
  } catch (error) {
    console.error('Error deleting task:', error);
    throw error;
  }
};

export const getUserTasks = async (userId) => {
  try {
    const q = query(
      collection(db, 'tasks'),
      where('userId', '==', userId),
      orderBy('createdAt', 'desc')
    );
    const querySnapshot = await getDocs(q);
    const tasks = [];
    querySnapshot.forEach((doc) => {
      tasks.push({ id: doc.id, ...doc.data() });
    });
    return tasks;
  } catch (error) {
    console.error('Error fetching tasks:', error);
    throw error;
  }
};
