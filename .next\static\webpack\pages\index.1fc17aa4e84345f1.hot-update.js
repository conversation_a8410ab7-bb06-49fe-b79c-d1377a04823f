"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./components/VectalDashboard.js":
/*!***************************************!*\
  !*** ./components/VectalDashboard.js ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ VectalDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../contexts/AuthContext */ \"./contexts/AuthContext.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/store */ \"./lib/store.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_Filter_Grid3X3_List_LogOut_MessageSquare_MoreHorizontal_Plus_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,Filter,Grid3X3,List,LogOut,MessageSquare,MoreHorizontal,Plus,Search,Settings,User!=!lucide-react */ \"__barrel_optimize__?names=Bell,Calendar,Filter,Grid3X3,List,LogOut,MessageSquare,MoreHorizontal,Plus,Search,Settings,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _VectalSidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./VectalSidebar */ \"./components/VectalSidebar.js\");\n/* harmony import */ var _VectalKanban__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./VectalKanban */ \"./components/VectalKanban.js\");\n/* harmony import */ var _VectalTaskModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./VectalTaskModal */ \"./components/VectalTaskModal.js\");\n/* harmony import */ var _VectalAIChat__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./VectalAIChat */ \"./components/VectalAIChat.js\");\n/* harmony import */ var _ThemeToggle__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ThemeToggle */ \"./components/ThemeToggle.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction VectalDashboard() {\n    _s();\n    const { user, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { tasks } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.useTaskStore)();\n    const [activeView, setActiveView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"kanban\");\n    const [showTaskModal, setShowTaskModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAIChat, setShowAIChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleLogout = async ()=>{\n        try {\n            await logout();\n        } catch (error) {\n            console.error(\"Failed to log out:\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen bg-background flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:block\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VectalSidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    activeView: activeView,\n                    onViewChange: setActiveView,\n                    onNewTask: ()=>setShowTaskModal(true),\n                    onToggleAI: ()=>setShowAIChat(!showAIChat)\n                }, void 0, false, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col min-w-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"bg-card border-b border-border px-6 py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-semibold text-foreground\",\n                                            children: activeView === \"kanban\" ? \"Kanban Board\" : \"Task List\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: [\n                                                    tasks.length,\n                                                    \" tasks\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                                                lineNumber: 62,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_Filter_Grid3X3_List_LogOut_MessageSquare_MoreHorizontal_Plus_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__.Search, {\n                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Search tasks...\",\n                                                    value: searchQuery,\n                                                    onChange: (e)=>setSearchQuery(e.target.value),\n                                                    className: \"vectal-input pl-10 w-64\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                                            lineNumber: 70,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center bg-muted rounded-lg p-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setActiveView(\"kanban\"),\n                                                    className: \"p-2 rounded-md transition-all \".concat(activeView === \"kanban\" ? \"bg-background shadow-sm text-foreground\" : \"text-muted-foreground hover:text-foreground\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_Filter_Grid3X3_List_LogOut_MessageSquare_MoreHorizontal_Plus_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__.Grid3X3, {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setActiveView(\"list\"),\n                                                    className: \"p-2 rounded-md transition-all \".concat(activeView === \"list\" ? \"bg-background shadow-sm text-foreground\" : \"text-muted-foreground hover:text-foreground\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_Filter_Grid3X3_List_LogOut_MessageSquare_MoreHorizontal_Plus_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__.List, {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                                                        lineNumber: 101,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowTaskModal(true),\n                                            className: \"vectal-button-primary flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_Filter_Grid3X3_List_LogOut_MessageSquare_MoreHorizontal_Plus_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__.Plus, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Add Task\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ThemeToggle__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"p-2 hover:bg-muted rounded-lg transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_Filter_Grid3X3_List_LogOut_MessageSquare_MoreHorizontal_Plus_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__.Bell, {\n                                                        className: \"h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 pl-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-primary rounded-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_Filter_Grid3X3_List_LogOut_MessageSquare_MoreHorizontal_Plus_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__.User, {\n                                                                className: \"h-4 w-4 text-primary-foreground\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                                                                lineNumber: 122,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-foreground\",\n                                                            children: (user === null || user === void 0 ? void 0 : user.displayName) || \"User\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleLogout,\n                                                            className: \"p-1 hover:bg-muted rounded transition-colors\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_Filter_Grid3X3_List_LogOut_MessageSquare_MoreHorizontal_Plus_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__.LogOut, {\n                                                                className: \"h-4 w-4 text-muted-foreground\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                                                                lineNumber: 131,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                                                            lineNumber: 127,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-hidden\",\n                        children: activeView === \"kanban\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VectalKanban__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            searchQuery: searchQuery\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                            lineNumber: 142,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_Filter_Grid3X3_List_LogOut_MessageSquare_MoreHorizontal_Plus_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__.List, {\n                                        className: \"h-12 w-12 text-muted-foreground mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                                        lineNumber: 146,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-foreground mb-2\",\n                                        children: \"List View\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                                        lineNumber: 147,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Coming soon...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                                        lineNumber: 148,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                                lineNumber: 145,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                            lineNumber: 144,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            showAIChat && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VectalAIChat__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                onClose: ()=>setShowAIChat(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                lineNumber: 157,\n                columnNumber: 9\n            }, this),\n            showTaskModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VectalTaskModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: showTaskModal,\n                onClose: ()=>setShowTaskModal(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n                lineNumber: 162,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VectalDashboard.js\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n_s(VectalDashboard, \"LYSNk7dUVlbfDRDg+LzUIgL7t10=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _lib_store__WEBPACK_IMPORTED_MODULE_3__.useTaskStore\n    ];\n});\n_c = VectalDashboard;\nvar _c;\n$RefreshReg$(_c, \"VectalDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/VectalDashboard.js\n"));

/***/ })

});