"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./contexts/AuthContext.js":
/*!*********************************!*\
  !*** ./contexts/AuthContext.js ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/firebase-config */ \"./lib/firebase-config.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/store */ \"./lib/store.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({});\nconst useAuth = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const { setUser: setStoreUser, loadTasks } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_4__.useTaskStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!_lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__.auth) {\n            console.log(\"Auth not initialized\");\n            setLoading(false);\n            return;\n        }\n        console.log(\"Setting up auth state listener\");\n        const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.onAuthStateChanged)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__.auth, async (user)=>{\n            console.log(\"Auth state changed:\", user ? \"User logged in\" : \"User logged out\");\n            console.log(\"User object:\", user);\n            setUser(user);\n            setStoreUser(user);\n            if (user) {\n                console.log(\"Loading tasks for user:\", user.uid);\n                await loadTasks(user.uid);\n            }\n            setLoading(false);\n        });\n        return unsubscribe;\n    }, [\n        setStoreUser,\n        loadTasks\n    ]);\n    const signup = async (email, password, displayName)=>{\n        const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.createUserWithEmailAndPassword)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__.auth, email, password);\n        await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.updateProfile)(userCredential.user, {\n            displayName\n        });\n        return userCredential;\n    };\n    const login = async (email, password)=>{\n        return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signInWithEmailAndPassword)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__.auth, email, password);\n    };\n    const logout = async ()=>{\n        return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signOut)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__.auth);\n    };\n    const value = {\n        user,\n        signup,\n        login,\n        logout,\n        loading\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\contexts\\\\AuthContext.js\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(AuthProvider, \"Ivq8ppwGxF5LjF6Nd4AqADFHJ0E=\", false, function() {\n    return [\n        _lib_store__WEBPACK_IMPORTED_MODULE_4__.useTaskStore\n    ];\n});\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./contexts/AuthContext.js\n"));

/***/ })

});